import { imageToBase64 } from '../common/ImageHelpers';
import { encode } from 'gpt-tokenizer';
import { GeminiLLMRepository } from '../../src/llm/GeminiLLMRepository';
import { monolithicFormPromptInstructions } from '../../src/workflow/utils/constants';
import { LLMResponse } from '../../src/llm/types/llm-response';

const GEMINI_API_KEY = 'AIzaSyDzh7xuznZzDi0c0DCYegXqGDH3UxFF3DQ'; // TODO: ADD API KEY
const DEFAULT_ITERATIONS = 5;

const TEST_PROMPTS = {
  simple: `
Is the user authenticated? Answer yes or no, nothing else
  `,
  medium: `
Provide an HTMX code that renders only the controls needed to complete that form.
### HTMX Form Requirements:
- Main form element: <form class="form-container" hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML">
- Primary Login button MUST be a <button> element and MUST include: hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-trigger="click". This is a critical requirement.
- ALL other buttons must also include: hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML".
- ALL input fields in the HTMX form must be empty even if the screenshot shows them filled
- Strictly focus on elements that are needed to complete the form to login. Skip buttons like "forget password"

### CSS Classes (use these exact classes):
- form-container
- input-container: wrapper for all input types
- **FLOATING LABEL INPUTS (preferred for text/password/email inputs):**
  - floating-input-wrapper: wrapper div for floating label inputs
  - floating-input: the input element (use instead of input-field)
  - floating-label: the label element (use instead of form-label)
  - Structure: <div class="input-container"><div class="floating-input-wrapper"><input class="floating-input" name="" placeholder="" type=""><label class="floating-label"></div></div>
- **LEGACY INPUTS (only for special cases):**
  - form-label, input-field: traditional label above input
- select-container, select-field
- checkbox-container, checkbox-field, checkbox-label
- radio-container, radio-option, radio-field
- button-container, button-primary, button-secondary
- form-error, otp-container, otp-input
- form-section, form-section-title

### Input Field Guidelines:
- **USE traditional labels for:** checkboxes, radio buttons, select dropdowns
- **ALWAYS set placeholder="" (empty) for floating inputs**
- **ALWAYS include proper for/id attributes for accessibility**

### Example Floating Label Structure:
For username input: input-container > floating-input-wrapper > (floating-input + floating-label)
For password input: input-container > floating-input-wrapper > (floating-input + floating-label)
Remember: floating-input must have placeholder="" and floating-label must come after the input

Response example:
Required JSON format:
{
  "htmx": "<form class="form-container" hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML"><div class="input-container"><div class="floating-input-wrapper"> <input class="floating-input" name="email" placeholder="" type="email"> <label class="floating-label">Enter your email</label></div></div><div class="input-container"><div class="floating-input-wrapper"> <input class="floating-input" name="password" placeholder="" type="password"> <label class="floating-label">Enter your password</label></div></div><div class="form-error">Wrong password. Try again or click Forgot password to reset it.</div><div class="button-container"><button class="button-primary" hx-ws="send" hx-target="#connection-flow" hx-swap="innerHTML" hx-trigger="click">Next</button></div></form>"
}
  `,
  complex: `
${monolithicFormPromptInstructions}
  `,
};

interface TestResult {
  duration: number;
  promptType: string;
  outputTokens: number;
  outputText: string;
  timestamp: number;
}

interface TestStatistics {
  average: number;
  min: number;
  max: number;
  median: number;
  stdDev: number;
  total: number;
  count: number;
}

function calculateStatistics(values: number[]): TestStatistics {
  if (values.length === 0) {
    throw new Error('Cannot calculate statistics on empty array');
  }

  const sorted = [...values].sort((a, b) => a - b);

  const total = values.reduce((sum, val) => sum + val, 0);
  const average = total / values.length;

  const mid = Math.floor(sorted.length / 2);
  const median = sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];

  const squareDiffs = values.map((value) => {
    const diff = value - average;
    return diff * diff;
  });
  const avgSquareDiff = squareDiffs.reduce((sum, val) => sum + val, 0) / values.length;
  const stdDev = Math.sqrt(avgSquareDiff);

  return {
    average,
    min: sorted[0],
    max: sorted[sorted.length - 1],
    median,
    stdDev,
    total,
    count: values.length,
  };
}

function formatStatsRow(label: string, stats: TestStatistics): string {
  return `| ${label} | ${stats.average.toFixed(2)} | ${stats.min.toFixed(2)} | ${stats.max.toFixed(2)} | ${stats.median.toFixed(2)} | ${stats.stdDev.toFixed(2)} |`;
}

function countTokens(text: string): number {
  return encode(text).length;
}

async function runGeminiTest(
  imageData: string,
  prompt: string,
  maxTokens: number = 150,
): Promise<TestResult> {
  const startTime = Date.now();

  try {
    const imageData = await imageToBase64(
      `${__dirname.replace('/scripts', '/files/screenshots/google_login.webp')}`,
    );

    const geminiRepository = new GeminiLLMRepository(
      GEMINI_API_KEY,
      'https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/google-ai-studio',
    );

    const response = await geminiRepository.getLLMResponse({
      platform: 'facebook',
      prompt: prompt,
      screenshot: imageData,
      skipCache: true,
      viewportWidth: 1080,
      viewportHeight: 720,
      version: 'v1',
    });

    const endTime = Date.now();
    const duration = endTime - startTime;

    const outputText = response.output_text;

    return {
      duration,
      promptType:
        prompt === TEST_PROMPTS.simple
          ? 'simple'
          : prompt === TEST_PROMPTS.medium
            ? 'medium'
            : 'complex',
      outputTokens: countTokens(outputText),
      outputText,
      timestamp: endTime,
    };
  } catch (error) {
    console.error('Error calling Gemini API:', error);
    throw error;
  }
}

export async function testPerformance(iterations: number = DEFAULT_ITERATIONS) {
  if (!GEMINI_API_KEY) {
    console.log('Please insert your Gemini API key in the script');
    return;
  }

  console.log('Loading and preparing test image...');
  const imageData = await imageToBase64(
    `${__dirname.replace('/scripts', '/files/screenshots/google_login.webp')}`,
  );
  console.log(`Image prepared successfully`);

  const results: Record<string, TestResult[]> = {
    simple: [],
    medium: [],
    complex: [],
  };

  console.log(`\n# Testing Gemini Model Performance`);
  console.log(`Running ${iterations} iterations for each prompt complexity level...\n`);

  for (const [promptType, prompt] of Object.entries(TEST_PROMPTS)) {
    console.log(`\n## Testing ${promptType} prompts`);

    for (let i = 0; i < iterations; i++) {
      process.stdout.write(`Running iteration ${i + 1}/${iterations}... `);

      try {
        const result = await runGeminiTest(imageData, prompt);
        results[promptType].push(result);
        process.stdout.write(`Done (${result.duration}ms)\n`);
      } catch (error) {
        process.stdout.write(`Failed\n`);
        console.error(`Error in ${promptType} prompt, iteration ${i + 1}:`, error);
      }
    }
  }

  // Calculate statistics
  const durationStats: Record<string, TestStatistics> = {};
  const tokenStats: Record<string, TestStatistics> = {};

  for (const promptType of Object.keys(results)) {
    if (results[promptType].length > 0) {
      durationStats[promptType] = calculateStatistics(results[promptType].map((r) => r.duration));
      tokenStats[promptType] = calculateStatistics(results[promptType].map((r) => r.outputTokens));
    }
  }

  console.log('\n# Gemini Model Performance\n');

  console.log('## Response Time Comparison (ms)\n');
  console.log('| Prompt Type | Avg (ms) | Min (ms) | Max (ms) | Median (ms) | Std Dev |');
  console.log('|-------------|----------|----------|----------|-------------|---------|');

  for (const promptType of Object.keys(durationStats)) {
    console.log(formatStatsRow(promptType, durationStats[promptType]));
  }

  console.log('\n## Output Token Comparison\n');
  console.log('| Prompt Type | Avg Tokens | Min Tokens | Max Tokens | Median Tokens | Std Dev |');
  console.log('|-------------|------------|------------|------------|---------------|---------|');

  for (const promptType of Object.keys(tokenStats)) {
    console.log(formatStatsRow(promptType, tokenStats[promptType]));
  }

  console.log('\n## Token Generation Speed (tokens/second)\n');
  console.log('| Prompt Type | Tokens/Second |');
  console.log('|-------------|---------------|');

  for (const promptType of Object.keys(durationStats)) {
    const tokensPerSecond =
      tokenStats[promptType].average / (durationStats[promptType].average / 1000);
    console.log(`| ${promptType} | ${tokensPerSecond.toFixed(2)} |`);
  }

  // Sample outputs
  console.log('\n## Sample Outputs\n');

  for (const promptType of Object.keys(results)) {
    if (results[promptType].length > 0) {
      console.log(`### ${promptType} prompt\n`);
      console.log('```');
      // @ts-ignore
      console.log(`Prompt: ${TEST_PROMPTS[promptType]}`);
      console.log('```\n');
      console.log('```');
      console.log(results[promptType][0].outputText);
      console.log('```\n');
    }
  }
}

async function runSingleCall(prompt: string): Promise<LLMResponse> {
  if (!GEMINI_API_KEY) {
    console.log('Please insert your Gemini API key in the script');
    throw new Error('Please insert your Gemini API key in the script');
  }
  console.log('Loading...');
  const startTime = Date.now();

  try {
    const imageData = await imageToBase64(`${__dirname.replace('/scripts', workingFile)}`);

    const geminiRepository = new GeminiLLMRepository(
      GEMINI_API_KEY,
      'https://gateway.ai.cloudflare.com/v1/dc1aae994a7608ab1e59a843ff0e5a31/app-dev/google-ai-studio',
    );

    const response = await geminiRepository.getLLMResponse({
      platform: 'facebook',
      prompt: prompt,
      screenshot: imageData,
      skipCache: true,
      viewportWidth: 1080,
      viewportHeight: 720,
      version: 'v1',
    });
    console.log(response.output_text);

    const endTime = Date.now();
    const duration = endTime - startTime;
    console.log(`Duration: ${duration}ms`);

    return response;
  } catch (error) {
    console.error('Error calling Gemini API:', error);
    throw error;
  }
}

const action = process.argv[2];
const iterations = process.argv[3] ? parseInt(process.argv[3], 10) : DEFAULT_ITERATIONS;

export const FORM_VISION_PROMPT_V6 = `
You are an expert-level vision-language AI assistant. Your sole purpose is to analyze a screenshot of a user interface and produce a structured JSON data object that represents the primary authentication form on that screen.

CRITICAL: Your response must be ONLY a JSON object with no explanatory text, reasoning, or commentary.

Execute this task by adhering to the following rules and guidelines with absolute precision.

################################################################################
#                                 CORE DIRECTIVE                                 #
################################################################################

From the provided screenshot, identify and capture only the essential, interactive elements required for a user to complete the primary, "happy-path" authentication flow OR to bypass a non-automatable step. Your output must be a single, raw JSON object.

################################################################################
#                            CRITICAL: FIRST CHECK                              #
################################################################################

**Before doing ANYTHING else, you MUST determine if the user is already authenticated.**

Look for these AUTHENTICATED PAGE indicators:
- User dashboard, avatar, name, or account information displayed
- Navigation menus showing logged-in user options
- Prompts related to account/device trust or login verification (e.g., “You're logged in. Trust this device?”)

################################################################################
#                                  MASTER RULES                                  #
################################################################################

These rules are your highest priority. However, the Special-Case Logic section can override them for specific screen types.

### RULE 1: THE PRINCIPLE OF MINIMAL NECESSARY ELEMENTS
Omit any element not indispensable to the primary authentication flow. This includes password-reset links, help links, sign-up buttons, social logins, "remember me" toggles, language switchers, and pre-filled, non-interactive user information.

### RULE 2: JSON-ONLY OUTPUT (CRITICAL)
Your entire response **MUST** be a single, valid JSON object with NO additional text, explanations, or commentary.
- Do **NOT** wrap it in markdown code blocks or backticks
- Do **NOT** include any text before or after the JSON
- The first character of your output must be \`{\` and the final character must be \`}\`

### RULE 3: GUIDANCE FOR HUMAN ACTION
- If the screen requires a specific action from a human user (e.g., entering a password), provide concise guidance (≤ 240 chars) in the \\\`metadata.prompt\\\` field.
- If a visible verification code (like a number to match) is displayed, extract the **digits only** and store them in the \\\`metadata.promptCode\\\` field.

### RULE 4: CAPTURE ALL STATUS AND ERROR MESSAGES (REVISED)
You must diligently look for and capture any visible text that communicates a status, validation error, or security notice related to the authentication attempt.
-   **What to Capture:** This includes classic errors (e.g., "Invalid password", "User not found") as well as informational alerts or security notices (e.g., "Your password was changed 20 days ago", "Too many attempts").
-   **Where to Place It:** This text must be captured, exactly as seen, in the \\\`metadata.error\\\` field.
-   **Context:** These messages are often displayed directly below an input field or as a banner at the top of the form. Capture them regardless of their color (red, black, etc.).

### RULE 5: RADIO BUTTON GROUPING (CRITICAL)
When you encounter multiple radio buttons that belong to the same logical group, you MUST group them into a SINGLE field entry with multiple options. Create ONE field with type="radio" and populate the options array with ALL available choices. DO NOT create separate field entries for each radio button in the same group.

### RULE 6: FINAL SELF-VALIDATION CHECK
After generating the JSON, perform a self-check. If any core rule was violated (and not overridden by a special case), your output is INVALID. Regenerate once. If still invalid, return only the single token \`INVALID_OUTPUT\`.

################################################################################
#                                SPECIAL-CASE LOGIC                              #
#      If a screen matches one of these cases, apply its logic exclusively.      #
################################################################################

### CASE 1: COMPULSORY PASSKEY PROMPT (HIGHEST PRIORITY CASE)
This case applies when the screen's primary action is to use a passkey, and our goal is to automatically bypass it.

**A. Detection Cues:** Must meet ALL criteria:
1.  **Headline:** Contains "Use your passkey" or similar.
2.  **Primary Action:** A "Continue" button intended to trigger the native passkey prompt.
3.  **Bypass Path:** A "Try another way" or similar link/button is present.
4.  **No Credential Fields:** No visible password/OTP fields.

**B. Required Output:** Generate a JSON object where \\\`controls.buttons\\\` contains **ONLY ONE** element: the "Try another way" button, with its \\\`actor\\\` set to \\\`"ai"\\\`. Omit the "Continue" button completely. Set \\\`metadata.prompt\\\` to "Passkey screen detected. Automatically selecting alternative sign-in method."

### CASE 2: DEVICE ACKNOWLEDGEMENT (PUSH NOTIFICATION OR NUMBER MATCH)
This case applies when the screen is paused, waiting for approval from a second device.

**A. Detection Cues:** Must meet a majority (≥3) of these cues:
1.  **Headline:** Contains “2-Step Verification”, “Approve sign-in”, etc.
2.  **Instructions:** Text says to act on another device ("Open your app...").
3.  **Visual State Indicator:** A spinner, phone icon, or a large number to match.
4.  **Alternative Path Link:** Presence of a "Try another way" link.
5.  **User Identifier:** A visible, non-interactive email/username.

**B. Required Output:** The \\\`controls\\\` object MUST contain ONLY:
1.  The mandatory synthetic "device-ack" button (\\\`actor: "human"\\\`).
2.  (Optional) Any "Resend notification" links.
Do NOT include "Try another way" or "Don't ask again".

### CASE 3: LOADING STATE DETECTION
This case applies when the page is still loading content and no meaningful form elements are visible yet.

**A. Detection Cues:** Must meet ALL criteria:
1.  **Minimal Content:** The page shows only basic elements like a title, logo, or loading indicators.
2.  **No Interactive Elements:** No visible input fields, buttons, or form controls are present.
3.  **Loading Indicators:** May show spinners, progress bars, "Loading..." text, or similar loading states.


### CASE 4: STANDARD FORMS & OTHER SCREENS
If the screen does not match Cases 1, 2, or 3, apply the Master Rules. For a standard login/MFA form, capture the necessary credential fields, the primary submit button, and any error messages as per Rule 4.

### CASE 4: Keep me signed in
1. For any checkbox with a label or name matching “keep me logged in”/“remember me”/“stay signed in”/“remember my device” or anything that implies keeping the session longer. The response must:  
   a. Actor === AI  
   b. Fixed field ID “keep-me-logged-in” to differentiate from others checkbox  
   c. Detect checked/unchecked, and type “checkbox”

################################################################################
#                              JSON OUTPUT SCHEMA                              #
################################################################################

\`\`\`json
{
  "metadata": {
    "title": "string",                  // The primary, most prominent heading on the page or the site name if no heading exists.
    "description": "string",            // Any visible, non-interactive, static text that provides additional context about the form, but is not a direct instruction.
    "prompt": "string|null",            // Actionable guidance for the human user as defined in Rule 3. Null if no specific action is needed.
    "promptCode": "string|null",        // Any visible verification code (digits only) as defined in Rule 3. Null if not present.
    "errors": "string[]",               // Any visible validation error, security notice, or status message related to an input. Null if none.
    "pageType": "string"                // Must be one of: "authenticated" (user is already logged in), "not-authenticated" (login form), "captcha" (CAPTCHA challenge), "loading" (page is still loading content), "other" (unrecognized screen).
  },
  "controls": {
    "fields": [
      {
        "id": "string",                 // A unique, descriptive ID for the element, derived from its label or placeholder (e.g., "email-address").
        "order": "number",              // The 1-based visual rendering order of the field on the page, from top to bottom.
        "actor": "string",              // Who interacts with this field. For all data entry fields, this MUST be "human".
        "label": "string",              // The visible label text associated with the field.
        "type": "string",               // The field's data type. Must be one of: "text", "password", "email", "number", "checkbox", "radio", "textarea", "other".
        "actiontype": "string",         // The type of interaction. Must be "fill" for text-based inputs, "select" for checkboxes/radios, or "click" for buttons.
        "name": "string",          // The 'name' attribute from the HTML. This will be used for form submission. If not available in HTML, use the 'id' attribute.
        "options": [                    // Used only for 'radio' or 'select' elements. An array of available options. Null for other field types.
          {"value": "string", "label": "string"}  // CRITICAL: For radio groups, include ALL options in ONE field, not separate fields per option
        ]|null,
        "readOnly": "boolean"           // Must be 'false' in the final output, per Rule 4. Indicates if the field is editable.
        "checked": "boolean"            // Indicates if the checkbox is selected
      }
    ],
    "buttons": [
      {
        "id": "string",                 // A unique, descriptive ID for the button, derived from its label (e.g., "sign-in").
        "order": "number",              // The 1-based visual rendering order of the button on the page.
        "actor": "string",              // Who interacts with this button. "human" for primary submission, "ai" for automatic bypass clicks (rare).
        "label": "string",              // The visible text on the button (e.g., "Continue").
        "variant": "string",            // The button's visual style. Must be one of: "primary" (the main action), "secondary" (a less important action), "link" (looks like a hyperlink).
        "type": "string",               // The button's function. Must be one of: "submit" (submits credentials), "click" (a generic click action), "device-ack" (the synthetic button from Special Case 1).
        "actiontype": "string",         // The type of interaction. Must be "click" for most buttons, or "acknowledge" for the synthetic device-ack button.
        "synthetic": "boolean"          // 'true' only if the button was programmatically added by you (e.g., the device-ack button), 'false' otherwise.
      }
    ]
  }
}
\`\`\`

Begin analysis now.
`;

const workingFile = '/files/screenshots/microsoft-keep-me-signed-in.png';
console.log(workingFile);
switch (action) {
  case 'single':
    runSingleCall(FORM_VISION_PROMPT_V6);
    break;
  case 'performance':
    testPerformance(iterations);
    break;
}
